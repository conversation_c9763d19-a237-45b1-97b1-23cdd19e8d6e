<template>
  <ant-modal
    :visible="open"
    :modal-title="modalTitle"
    :loading="modalLoading"
    modalWidth="1600"
    @cancel="cancel"
    modalHeight="900"
  >
    <div slot="content" style="height: 100%; display: flex; flex-direction: column">
      <!-- 上方方案信息表格 -->
      <div style="height: 200px; margin-bottom: 20px">
        <div style="font-size: 16px; font-weight: bold; margin-bottom: 10px">方案对比信息</div>
        <a-table
          :columns="infoColumns"
          :data-source="compareData"
          :pagination="false"
          size="small"
          bordered
        />
      </div>

      <!-- 下方图表区域 -->
      <div style="flex: 1; display: flex; flex-direction: column">
        <div style="font-size: 16px; font-weight: bold; margin-bottom: 10px">图表对比</div>
        
        <!-- 第一行：雨量表和水位表 -->
        <div style="height: 42%; display: flex; margin-bottom: 15px">
          <div style="width: 50%; margin-right: 10px">
            <div style="font-size: 14px; font-weight: bold; margin-bottom: 5px; text-align: center">雨量对比</div>
            <div style="height: calc(100% - 25px); padding-bottom: 10px;">
              <RainfallChart :dataSource="rainfallChartData" />
            </div>
          </div>
          <div style="width: 50%">
            <div style="font-size: 14px; font-weight: bold; margin-bottom: 5px; text-align: center">水位对比</div>
            <div style="height: calc(100% - 25px); padding-bottom: 10px;">
              <WaterLevelChart :dataSource="waterLevelChartData" />
            </div>
          </div>
        </div>

        <!-- 第二行：入库流量表、供水流量表、泄洪流量表 -->
        <div style="height: 42%; display: flex">
          <div style="width: 33.33%; margin-right: 10px">
            <div style="font-size: 14px; font-weight: bold; margin-bottom: 5px; text-align: center">入库流量对比</div>
            <div style="height: calc(100% - 25px); padding-bottom: 10px;">
              <FlowChart :dataSource="inflowChartData" chartType="入库流量" />
            </div>
          </div>
          <div style="width: 33.33%; margin-right: 10px">
            <div style="font-size: 14px; font-weight: bold; margin-bottom: 5px; text-align: center">供水流量对比</div>
            <div style="height: calc(100% - 25px); padding-bottom: 10px;">
              <FlowChart :dataSource="supplyFlowChartData" chartType="供水流量" />
            </div>
          </div>
          <div style="width: 33.33%">
            <div style="font-size: 14px; font-weight: bold; margin-bottom: 5px; text-align: center">泄洪流量对比</div>
            <div style="height: calc(100% - 25px); padding-bottom: 10px;">
              <FlowChart :dataSource="floodFlowChartData" chartType="泄洪流量" />
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <template slot="footer">
      <a-button @click="cancel">关闭</a-button>
    </template>
  </ant-modal>
</template>

<script lang="jsx">
  import { getDispRes } from '../services'
  import AntModal from '@/components/pt/dialog/AntModal'
  import RainfallChart from './CompareModal/RainfallChart.vue'
  import WaterLevelChart from './CompareModal/WaterLevelChart.vue'
  import FlowChart from './CompareModal/FlowChart.vue'

  export default {
    name: 'CompareModal',
    components: { 
      AntModal, 
      RainfallChart, 
      WaterLevelChart, 
      FlowChart 
    },
    data() {
      return {
        loading: false,
        modalLoading: false,
        open: false,
        modalTitle: '方案对比',
        compareIds: [],
        compareData: [],
        resultData1: null,
        resultData2: null,
        
        // 表格列配置
        infoColumns: [
          { title: '方案', dataIndex: 'index', key: 'index', width: 80 },
          { title: '方案名称', dataIndex: 'caseName', key: 'caseName', width: 200 },
          { title: '方案编码', dataIndex: 'caseCode', key: 'caseCode', width: 200 },
          { title: '预报时段', dataIndex: 'forecastPeriod', key: 'forecastPeriod', width: 300 },
          { title: '调度方式', dataIndex: 'dispatchType', key: 'dispatchType', width: 150 },
        ],
        
        // 图表数据
        rainfallChartData: [],
        waterLevelChartData: [],
        inflowChartData: [],
        supplyFlowChartData: [],
        floodFlowChartData: [],
      }
    },
    computed: {},
    watch: {},
    methods: {
      // 取消按钮
      cancel() {
        this.open = false
        this.$emit('close')
      },
      
      async handleShow(ids, dispatchModelOptions) {
        this.open = true
        this.modalLoading = true
        this.compareIds = ids
        
        try {
          // 并行获取两个方案的详情数据
          const [res1, res2] = await Promise.all([
            getDispRes({ resvrDispId: ids[0] }),
            getDispRes({ resvrDispId: ids[1] })
          ])
          
          this.resultData1 = res1.data
          this.resultData2 = res2.data
          
          // 构建表格数据
          this.compareData = [
            {
              key: '1',
              index: '方案一',
              caseName: this.resultData1.caseName,
              caseCode: this.resultData1.caseCode,
              forecastPeriod: `${this.resultData1.startTime} - ${this.resultData1.endTime}`,
              dispatchType: dispatchModelOptions.find(el => el.value == this.resultData1.dispathType)?.label || '-'
            },
            {
              key: '2', 
              index: '方案二',
              caseName: this.resultData2.caseName,
              caseCode: this.resultData2.caseCode,
              forecastPeriod: `${this.resultData2.startTime} - ${this.resultData2.endTime}`,
              dispatchType: dispatchModelOptions.find(el => el.value == this.resultData2.dispathType)?.label || '-'
            }
          ]
          
          // 构建图表数据
          this.buildChartData()
          
        } catch (error) {
          this.$message.error('获取方案详情失败')
          console.error(error)
        } finally {
          this.modalLoading = false
        }
      },
      
      buildChartData() {
        const data1 = this.resultData1?.resvrDispResList || []
        const data2 = this.resultData2?.resvrDispResList || []
        
        // 雨量数据
        this.rainfallChartData = [
          {
            name: this.resultData1.caseName,
            data: data1.map(el => [el.tm, el.rain])
          },
          {
            name: this.resultData2.caseName,
            data: data2.map(el => [el.tm, el.rain])
          }
        ]
        
        // 水位数据
        this.waterLevelChartData = [
          {
            name: this.resultData1.caseName,
            data: data1.map(el => [el.tm, el.wlv])
          },
          {
            name: this.resultData2.caseName,
            data: data2.map(el => [el.tm, el.wlv])
          }
        ]
        
        // 入库流量数据
        this.inflowChartData = [
          {
            name: this.resultData1.caseName,
            data: data1.map(el => [el.tm, el.inflow])
          },
          {
            name: this.resultData2.caseName,
            data: data2.map(el => [el.tm, el.inflow])
          }
        ]
        
        // 供水流量数据
        this.supplyFlowChartData = [
          {
            name: this.resultData1.caseName,
            data: data1.map(el => [el.tm, el.outflow])
          },
          {
            name: this.resultData2.caseName,
            data: data2.map(el => [el.tm, el.outflow])
          }
        ]
        
        // 泄洪流量数据
        this.floodFlowChartData = [
          {
            name: this.resultData1.caseName,
            data: data1.map(el => [el.tm, el.floodflow])
          },
          {
            name: this.resultData2.caseName,
            data: data2.map(el => [el.tm, el.floodflow])
          }
        ]
      }
    },
  }
</script>

<style lang="less" scoped>
  ::v-deep .modal-content {
    height: 100%;
  }
</style>
