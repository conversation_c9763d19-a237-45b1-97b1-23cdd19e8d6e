<template>
  <base-echart 
    id="water-level-compare-chart" 
    class="water-level-compare-chart" 
    :width="width" 
    :height="height" 
    :option="options" 
  />
</template>

<script>
  import BaseEchart from '@/components/Echarts/BaseEchart.vue'

  export default {
    components: {
      BaseEchart,
    },
    props: {
      dataSource: {
        type: Array,
        default: () => []
      },
      width: { default: '100%' },
      height: { default: '100%' },
      // 汛限水位值，默认为138.60m
      floodLimitLevel: {
        type: Number,
        default: 138.60,
      },
    },
    data() {
      return {}
    },
    computed: {
      options() {
        return this.getOptions(this.dataSource)
      },
    },
    methods: {
      getOptions(dataSource) {
        const colors = ['#EF8432', '#FF6B6B']
        
        const option = {
          color: colors,
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985',
              },
            },
            formatter: function (params) {
              let htmlStr = ''
              htmlStr += params[0].name.replace(/\-/g, '/') + '<br/>'
              for (let i = 0; i < params.length; i++) {
                let param = params[i]
                let seriesName = param.seriesName
                let value = param.value[1] === null ? '-' : param.value[1]
                let color = param.color
                
                htmlStr += `
                  <div style="display:flex; align-items:center; justify-content:space-between; margin-bottom:4px">
                    <span style="margin-right:20px">
                      <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${color}"></span>
                      ${seriesName}
                    </span>
                    <span>${value} m</span>
                  </div>
                `
              }
              return htmlStr
            },
            backgroundColor: '#F4F7FC',
            borderWidth: 0,
            padding: 10,
            textStyle: {
              color: '#000',
              fontSize: 12,
            },
          },
          legend: {
            show: true,
            top: 10,
            data: [...dataSource.map(item => item.name), '汛限水位'],
            textStyle: {
              fontSize: 12,
            },
          },
          grid: {
            left: '10%',
            right: '10%',
            top: '20%',
            bottom: '30%', // 增加底部空间给滑动条和横坐标标签
          },
          xAxis: {
            type: 'category',
            axisLabel: {
              fontSize: 10,
              rotate: 0, // 横着显示，不旋转
              interval: 0, // 显示所有标签
            },
            axisTick: {
              alignWithLabel: true,
              interval: 0, // 显示所有刻度线
            },
            // 设置固定的类目轴间距，扩大横坐标距离
            boundaryGap: ['5%', '5%'],
          },
          yAxis: {
            type: 'value',
            name: '水位(m)',
            nameLocation: 'middle',
            nameGap: 50,
            nameRotate: 90,
            axisLabel: {
              fontSize: 10,
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: '#cccccc',
              },
            },
          },
          dataZoom: [
            {
              type: 'slider',
              show: true,
              xAxisIndex: [0],
              start: 0,
              end: 30, // 默认只显示30%的数据，确保横坐标有足够间距
              height: 20,
              bottom: 0,
              textStyle: {
                fontSize: 10
              }
            },
            {
              type: 'inside',
              xAxisIndex: [0],
              start: 0,
              end: 30
            }
          ],
          series: []
        }

        // 添加水位折线图
        dataSource.forEach((item, index) => {
          option.series.push({
            name: item.name,
            type: 'line',
            symbol: 'circle',
            symbolSize: 4,
            lineStyle: { 
              width: 3
            },
            data: item.data,
            itemStyle: {
              color: colors[index % colors.length]
            }
          })
        })

        // 添加汛限水位线
        if (dataSource.length > 0 && dataSource[0].data.length > 0) {
          const timeAxisData = dataSource[0].data.map(item => item[0])
          const floodLimitData = timeAxisData.map(time => [time, this.floodLimitLevel])
          
          option.series.push({
            name: '汛限水位',
            type: 'line',
            symbol: 'none',
            lineStyle: {
              width: 2,
              type: 'dashed',
              color: '#FF4D4F'
            },
            data: floodLimitData,
            itemStyle: {
              color: '#FF4D4F'
            }
          })
        }

        return option
      },
    },
  }
</script>

<style lang="less" scoped>
</style>
