<template>
  <!-- 图表滚动容器 -->
  <div
    ref="scrollContainer"
    style="width: 100%; height: 100%; overflow-x: auto; overflow-y: hidden; border: 1px solid #f0f0f0; border-radius: 4px;"
  >
    <div
      ref="chartContainer"
      :style="{ height: '100%', minWidth: chartWidth + 'px' }"
    ></div>
  </div>
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    props: {
      dataSource: {
        type: Array,
        default: () => []
      },
      width: { default: '100%' },
      height: { default: '100%' },
      // 汛限水位值，默认为138.60m
      floodLimitLevel: {
        type: Number,
        default: 138.60,
      },
    },
    data() {
      return {
        chart: null,
        chartWidth: 800
      }
    },
    computed: {
      calculatedWidth() {
        if (!this.dataSource || !this.dataSource[0] || !this.dataSource[0].data) {
          return 800
        }
        const dataLength = this.dataSource[0].data.length
        return Math.max(800, dataLength * 80)
      }
    },
    mounted() {
      this.initChart()
      window.addEventListener('resize', this.handleResize)
    },
    beforeDestroy() {
      if (this.chart) {
        this.chart.dispose()
      }
      window.removeEventListener('resize', this.handleResize)
    },
    watch: {
      dataSource: {
        handler() {
          this.updateChart()
        },
        deep: true
      }
    },
    methods: {
      initChart() {
        this.chart = echarts.init(this.$refs.chartContainer)
        this.updateChart()
      },

      handleResize() {
        if (this.chart) {
          this.chart.resize()
        }
      },

      updateChart() {
        if (!this.chart || !this.dataSource || !this.dataSource.length) return

        this.chartWidth = this.calculatedWidth
        this.$nextTick(() => {
          this.chart.setOption(this.getOptions(this.dataSource))
          this.chart.resize()
        })
      },

      getOptions(dataSource) {
        const colors = ['#EF8432', '#FF6B6B']
        
        const option = {
          color: colors,
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985',
              },
            },
            formatter: function (params) {
              let htmlStr = ''
              htmlStr += params[0].name.replace(/\-/g, '/') + '<br/>'
              for (let i = 0; i < params.length; i++) {
                let param = params[i]
                let seriesName = param.seriesName
                let value = param.value[1] === null ? '-' : param.value[1]
                let color = param.color
                
                htmlStr += `
                  <div style="display:flex; align-items:center; justify-content:space-between; margin-bottom:4px">
                    <span style="margin-right:20px">
                      <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${color}"></span>
                      ${seriesName}
                    </span>
                    <span>${value} m</span>
                  </div>
                `
              }
              return htmlStr
            },
            backgroundColor: '#F4F7FC',
            borderWidth: 0,
            padding: 10,
            textStyle: {
              color: '#000',
              fontSize: 12,
            },
          },
          legend: {
            show: true,
            top: 10,
            data: [...dataSource.map(item => item.name), '汛限水位'],
            textStyle: {
              fontSize: 12,
            },
          },
          grid: {
            left: 60,
            right: 60,
            top: 50,
            bottom: 80,
            containLabel: false
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: dataSource[0]?.data.map(item => item[0]) || [],
            axisLabel: {
              fontSize: 10,
              rotate: 0,
              interval: 0,
              margin: 15
            },
            axisTick: {
              alignWithLabel: true
            }
          },
          yAxis: {
            type: 'value',
            name: '水位(m)',
            nameLocation: 'middle',
            nameGap: 50,
            nameRotate: 90,
            axisLabel: {
              fontSize: 10,
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: '#cccccc',
              },
            },
          },
          series: []
        }

        // 添加水位折线图
        dataSource.forEach((item, index) => {
          option.series.push({
            name: item.name,
            type: 'line',
            symbol: 'circle',
            symbolSize: 4,
            lineStyle: {
              width: 3
            },
            data: item.data.map(d => d[1]), // 只取数值部分
            itemStyle: {
              color: colors[index % colors.length]
            }
          })
        })

        // 添加汛限水位线
        if (dataSource.length > 0 && dataSource[0].data.length > 0) {
          const floodLimitData = new Array(dataSource[0].data.length).fill(this.floodLimitLevel)

          option.series.push({
            name: '汛限水位',
            type: 'line',
            symbol: 'none',
            lineStyle: {
              width: 2,
              type: 'dashed',
              color: '#FF4D4F'
            },
            data: floodLimitData,
            itemStyle: {
              color: '#FF4D4F'
            }
          })
        }

        return option
      },
    },
  }
</script>

<style lang="less" scoped>
</style>
