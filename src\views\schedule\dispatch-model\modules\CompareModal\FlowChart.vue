<template>
  <!-- 图表滚动容器 -->
  <div
    ref="scrollContainer"
    style="width: 100%; height: 100%; overflow-x: auto; overflow-y: hidden; border: 1px solid #f0f0f0; border-radius: 4px;"
  >
    <div
      ref="chartContainer"
      :style="{ height: '100%', minWidth: chartWidth + 'px' }"
    ></div>
  </div>
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    props: {
      dataSource: {
        type: Array,
        default: () => []
      },
      chartType: {
        type: String,
        default: '流量'
      },
      width: { default: '100%' },
      height: { default: '100%' },
    },
    data() {
      return {
        chart: null,
        chartWidth: 800
      }
    },
    computed: {
      calculatedWidth() {
        if (!this.dataSource || !this.dataSource[0] || !this.dataSource[0].data) {
          return 800
        }
        const dataLength = this.dataSource[0].data.length
        return Math.max(800, dataLength * 80)
      },
      chartColors() {
        const colorMap = {
          '入库流量': ['#0FC6C2', '#20E2DE'],
          '供水流量': ['#3CB371', '#90EE90'],
          '泄洪流量': ['#FF69B4', '#FFB6C1']
        }
        return colorMap[this.chartType] || ['#507EF7', '#74C3F8']
      }
    },
    mounted() {
      this.initChart()
      window.addEventListener('resize', this.handleResize)
    },
    beforeDestroy() {
      if (this.chart) {
        this.chart.dispose()
      }
      window.removeEventListener('resize', this.handleResize)
    },
    watch: {
      dataSource: {
        handler() {
          this.updateChart()
        },
        deep: true
      }
    },
    methods: {
      initChart() {
        this.chart = echarts.init(this.$refs.chartContainer)
        this.updateChart()
      },

      handleResize() {
        if (this.chart) {
          this.chart.resize()
        }
      },

      updateChart() {
        if (!this.chart || !this.dataSource || !this.dataSource.length) return

        this.chartWidth = this.calculatedWidth
        this.$nextTick(() => {
          this.chart.setOption(this.getOptions(this.dataSource))
          this.chart.resize()
        })
      },

      getOptions(dataSource) {
        const colors = this.chartColors

        const option = {
          color: colors,
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            },
            formatter: (params) => {
              let result = params[0].axisValue + '<br/>'
              params.forEach(param => {
                result += `${param.marker}${param.seriesName}: ${param.value}m³/s<br/>`
              })
              return result
            }
          },
          legend: {
            show: true,
            top: 10,
            data: dataSource.map(item => item.name),
            textStyle: {
              fontSize: 12,
            },
          },
          grid: {
            left: 60,
            right: 60,
            top: 40,
            bottom: 60,
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: dataSource[0]?.data.map(item => item[0]) || [],
            axisLabel: {
              fontSize: 10,
              rotate: 0,
              interval: 1, // 空一个显示一个
              margin: 15
            },
            axisTick: {
              alignWithLabel: true
            }
          },
          yAxis: {
            type: 'value',
            name: `${this.chartType}(m³/s)`,
            nameLocation: 'middle',
            nameGap: 40,
            nameTextStyle: {
              fontSize: 12
            },
            axisLabel: {
              fontSize: 10,
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: '#cccccc',
              },
            },
          },
          series: []
        }

        // 添加流量折线图
        dataSource.forEach((item, index) => {
          option.series.push({
            name: item.name,
            type: 'line',
            symbol: 'circle',
            symbolSize: 4,
            lineStyle: {
              width: 3
            },
            data: item.data.map(d => d[1]), // 只取数值部分
            itemStyle: {
              color: colors[index % colors.length]
            }
          })
        })

        return option
      },
    },
  }
</script>

<style lang="less" scoped>
</style>
