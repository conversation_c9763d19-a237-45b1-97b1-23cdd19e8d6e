<template>
  <base-echart 
    id="flow-compare-chart" 
    class="flow-compare-chart" 
    :width="width" 
    :height="height" 
    :option="options" 
  />
</template>

<script>
  import BaseEchart from '@/components/Echarts/BaseEchart.vue'

  export default {
    components: {
      BaseEchart,
    },
    props: {
      dataSource: {
        type: Array,
        default: () => []
      },
      chartType: {
        type: String,
        default: '流量'
      },
      width: { default: '100%' },
      height: { default: '100%' },
    },
    data() {
      return {}
    },
    computed: {
      options() {
        return this.getOptions(this.dataSource)
      },
      chartColors() {
        const colorMap = {
          '入库流量': ['#0FC6C2', '#20E2DE'],
          '供水流量': ['#3CB371', '#90EE90'],
          '泄洪流量': ['#FF69B4', '#FFB6C1']
        }
        return colorMap[this.chartType] || ['#507EF7', '#74C3F8']
      }
    },
    methods: {
      getOptions(dataSource) {
        const colors = this.chartColors
        
        const option = {
          color: colors,
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985',
              },
            },
            formatter: function (params) {
              let htmlStr = ''
              htmlStr += params[0].name.replace(/\-/g, '/') + '<br/>'
              for (let i = 0; i < params.length; i++) {
                let param = params[i]
                let seriesName = param.seriesName
                let value = param.value[1] === null ? '-' : param.value[1]
                let color = param.color
                
                htmlStr += `
                  <div style="display:flex; align-items:center; justify-content:space-between; margin-bottom:4px">
                    <span style="margin-right:20px">
                      <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${color}"></span>
                      ${seriesName}
                    </span>
                    <span>${value} m³/s</span>
                  </div>
                `
              }
              return htmlStr
            },
            backgroundColor: '#F4F7FC',
            borderWidth: 0,
            padding: 10,
            textStyle: {
              color: '#000',
              fontSize: 12,
            },
          },
          legend: {
            show: true,
            top: 10,
            data: dataSource.map(item => item.name),
            textStyle: {
              fontSize: 12,
            },
          },
          grid: {
            left: '10%',
            right: '10%',
            top: '20%',
            bottom: '15%',
          },
          xAxis: {
            type: 'category',
            axisLabel: {
              fontSize: 10,
              rotate: 45,
              interval: 2, // 每隔2个显示一个标签，扩大间隔
            },
            axisTick: {
              alignWithLabel: true,
              interval: 2, // 刻度线也对应调整
            },
          },
          yAxis: {
            type: 'value',
            name: `${this.chartType}(m³/s)`,
            nameLocation: 'middle',
            nameGap: 50,
            nameRotate: 90,
            axisLabel: {
              fontSize: 10,
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: '#cccccc',
              },
            },
          },
          dataZoom: [
            {
              type: 'inside',
              xAxisIndex: [0],
              start: 0,
              end: 100 // 默认显示全部数据
            }
          ],
          series: []
        }

        // 添加流量折线图
        dataSource.forEach((item, index) => {
          option.series.push({
            name: item.name,
            type: 'line',
            symbol: 'circle',
            symbolSize: 4,
            lineStyle: { 
              width: 3
            },
            data: item.data,
            itemStyle: {
              color: colors[index % colors.length]
            }
          })
        })

        return option
      },
    },
  }
</script>

<style lang="less" scoped>
</style>
