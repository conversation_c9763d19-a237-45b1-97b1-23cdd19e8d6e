<template>
  <!-- 图表滚动容器 -->
  <div
    ref="scrollContainer"
    style="width: 100%; height: 100%; overflow-x: auto; overflow-y: hidden; border: 1px solid #f0f0f0; border-radius: 4px;"
  >
    <div
      ref="chartContainer"
      :style="{ height: '100%', minWidth: chartWidth + 'px' }"
    ></div>
  </div>
</template>

<script>
  import * as echarts from 'echarts'

  export default {
    props: {
      dataSource: {
        type: Array,
        default: () => []
      },
      width: { default: '100%' },
      height: { default: '100%' },
    },
    data() {
      return {
        chart: null,
        chartWidth: 800 // 默认最小宽度
      }
    },
    computed: {
      // 计算图表宽度，确保横坐标有足够间距
      calculatedWidth() {
        if (!this.dataSource || !this.dataSource[0] || !this.dataSource[0].data) {
          return 800
        }
        const dataLength = this.dataSource[0].data.length
        return Math.max(800, dataLength * 80) // 每个数据点80px宽度
      }
    },
    mounted() {
      this.initChart()
      window.addEventListener('resize', this.handleResize)
    },
    beforeDestroy() {
      if (this.chart) {
        this.chart.dispose()
      }
      window.removeEventListener('resize', this.handleResize)
    },
    watch: {
      dataSource: {
        handler() {
          this.updateChart()
        },
        deep: true
      }
    },
    methods: {
      initChart() {
        this.chart = echarts.init(this.$refs.chartContainer)
        this.updateChart()
      },

      handleResize() {
        if (this.chart) {
          this.chart.resize()
        }
      },

      updateChart() {
        if (!this.chart || !this.dataSource || !this.dataSource.length) return

        // 更新图表宽度
        this.chartWidth = this.calculatedWidth
        this.$nextTick(() => {
          this.chart.setOption(this.getOptions(this.dataSource))
          this.chart.resize()
        })
      },
      getOptions(dataSource) {
        const colors = ['#507EF7', '#74C3F8']
        
        const option = {
          color: colors,
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985',
              },
            },
            formatter: function (params) {
              let htmlStr = ''
              htmlStr += params[0].name.replace(/\-/g, '/') + '<br/>'
              for (let i = 0; i < params.length; i++) {
                let param = params[i]
                let seriesName = param.seriesName
                let value = param.value[1] === null ? '-' : param.value[1]
                let color = param.color
                
                htmlStr += `
                  <div style="display:flex; align-items:center; justify-content:space-between; margin-bottom:4px">
                    <span style="margin-right:20px">
                      <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${color}"></span>
                      ${seriesName}
                    </span>
                    <span>${value} mm</span>
                  </div>
                `
              }
              return htmlStr
            },
            backgroundColor: '#F4F7FC',
            borderWidth: 0,
            padding: 10,
            textStyle: {
              color: '#000',
              fontSize: 12,
            },
          },
          legend: {
            show: true,
            top: 10,
            data: dataSource.map(item => item.name),
            textStyle: {
              fontSize: 12,
            },
          },
          grid: {
            left: 60,
            right: 60,
            top: 40,
            bottom: 60,
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: dataSource[0]?.data.map(item => item[0]) || [],
            axisLabel: {
              fontSize: 10,
              rotate: 0, // 横着显示，不旋转
              interval: 1, // 空一个显示一个
              margin: 15
            },
            axisTick: {
              alignWithLabel: true
            }
          },
          yAxis: [
            {
              type: 'value',
              name: '时段雨量(mm)',
              nameLocation: 'middle',
              nameGap: 50,
              nameRotate: 90,
              axisLabel: {
                fontSize: 10,
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed',
                  color: '#cccccc',
                },
              },
            },
            {
              type: 'value',
              name: '累计降雨量(mm)',
              nameLocation: 'middle',
              nameGap: 50,
              nameRotate: 270,
              position: 'right',
              axisLabel: {
                fontSize: 10,
              },
              splitLine: {
                show: false,
              },
            }
          ],
          series: []
        }

        // 处理数据并添加系列
        dataSource.forEach((item, index) => {
          // 时段雨量柱状图
          option.series.push({
            name: item.name,
            type: 'bar',
            yAxisIndex: 0,
            barMaxWidth: 15,
            data: item.data.map(d => d[1]), // 只取数值部分
            itemStyle: {
              color: colors[index % colors.length]
            }
          })

          // 累计降雨量折线图
          const cumulativeData = item.data.map((el, idx) => {
            const sum = item.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0)
            return +sum.toFixed(1)
          })

          option.series.push({
            name: item.name + '(累计)',
            type: 'line',
            yAxisIndex: 1,
            symbol: 'circle',
            symbolSize: 4,
            lineStyle: {
              width: 2,
              type: 'dashed'
            },
            data: cumulativeData,
            itemStyle: {
              color: colors[index % colors.length]
            }
          })
        })

        return option
      },
    },
  }
</script>

<style lang="less" scoped>
</style>
