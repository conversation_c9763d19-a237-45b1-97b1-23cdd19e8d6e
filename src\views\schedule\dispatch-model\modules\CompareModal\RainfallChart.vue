<template>
  <base-echart 
    id="rainfall-compare-chart" 
    class="rainfall-compare-chart" 
    :width="width" 
    :height="height" 
    :option="options" 
  />
</template>

<script>
  import BaseEchart from '@/components/Echarts/BaseEchart.vue'

  export default {
    components: {
      BaseEchart,
    },
    props: {
      dataSource: {
        type: Array,
        default: () => []
      },
      width: { default: '100%' },
      height: { default: '100%' },
    },
    data() {
      return {}
    },
    computed: {
      options() {
        return this.getOptions(this.dataSource)
      },
    },
    methods: {
      getOptions(dataSource) {
        const colors = ['#507EF7', '#74C3F8']
        
        const option = {
          color: colors,
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985',
              },
            },
            formatter: function (params) {
              let htmlStr = ''
              htmlStr += params[0].name.replace(/\-/g, '/') + '<br/>'
              for (let i = 0; i < params.length; i++) {
                let param = params[i]
                let seriesName = param.seriesName
                let value = param.value[1] === null ? '-' : param.value[1]
                let color = param.color
                
                htmlStr += `
                  <div style="display:flex; align-items:center; justify-content:space-between; margin-bottom:4px">
                    <span style="margin-right:20px">
                      <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${color}"></span>
                      ${seriesName}
                    </span>
                    <span>${value} mm</span>
                  </div>
                `
              }
              return htmlStr
            },
            backgroundColor: '#F4F7FC',
            borderWidth: 0,
            padding: 10,
            textStyle: {
              color: '#000',
              fontSize: 12,
            },
          },
          legend: {
            show: true,
            top: 10,
            data: dataSource.map(item => item.name),
            textStyle: {
              fontSize: 12,
            },
          },
          grid: {
            left: '10%',
            right: '10%',
            top: '20%',
            bottom: '20%', // 增加底部空间给横坐标标签
          },
          xAxis: {
            type: 'category',
            axisLabel: {
              fontSize: 10,
              rotate: 0, // 横着显示，不旋转
              interval: 4, // 进一步扩大间隔，每隔4个显示一个标签
            },
            axisTick: {
              alignWithLabel: true,
              interval: 4, // 刻度线也对应调整
            },
          },
          yAxis: [
            {
              type: 'value',
              name: '时段雨量(mm)',
              nameLocation: 'middle',
              nameGap: 50,
              nameRotate: 90,
              axisLabel: {
                fontSize: 10,
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed',
                  color: '#cccccc',
                },
              },
            },
            {
              type: 'value',
              name: '累计降雨量(mm)',
              nameLocation: 'middle',
              nameGap: 50,
              nameRotate: 270,
              position: 'right',
              axisLabel: {
                fontSize: 10,
              },
              splitLine: {
                show: false,
              },
            }
          ],
          dataZoom: [
            {
              type: 'inside',
              xAxisIndex: [0],
              start: 0,
              end: 100 // 默认显示全部数据
            }
          ],
          series: []
        }

        // 处理数据并添加系列
        dataSource.forEach((item, index) => {
          // 时段雨量柱状图
          option.series.push({
            name: item.name,
            type: 'bar',
            yAxisIndex: 0,
            barMaxWidth: 15,
            data: item.data,
            itemStyle: {
              color: colors[index % colors.length]
            }
          })
          
          // 累计降雨量折线图
          const cumulativeData = item.data.map((el, idx) => {
            const sum = item.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0)
            return [el[0], +sum.toFixed(1)]
          })
          
          option.series.push({
            name: item.name + '(累计)',
            type: 'line',
            yAxisIndex: 1,
            symbol: 'circle',
            symbolSize: 4,
            lineStyle: { 
              width: 2,
              type: 'dashed'
            },
            data: cumulativeData,
            itemStyle: {
              color: colors[index % colors.length]
            }
          })
        })

        return option
      },
    },
  }
</script>

<style lang="less" scoped>
</style>
